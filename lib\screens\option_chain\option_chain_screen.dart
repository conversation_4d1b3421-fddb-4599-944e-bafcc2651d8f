import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:phoenix/features/option_greeks_websocket/bloc/option_greeks_websocket_bloc.dart';
import 'package:phoenix/features/websocket/model/proto/option_greeks/option_greeks.pb.dart';
import 'package:phoenix/features/authentication/bloc/auth_bloc.dart';
import 'package:phoenix/utils/app_theme.dart';
import 'package:phoenix/features/theme/bloc/theme_bloc.dart';
import 'package:phoenix/features/theme/bloc/theme_state.dart';
import 'package:phoenix/features/security_list/bloc/security_list_bloc.dart';
import 'package:phoenix/features/security_list/model/security_model.dart';
import 'package:phoenix/screens/orders/order_form_bottom_sheet.dart';
import 'package:phoenix/screens/orders/order_type.dart';
import 'package:phoenix/services/watchlist_service.dart';
import 'package:phoenix/widgets/toast/toast_utils.dart';
import 'package:phoenix/widgets/toast/custom_toast.dart';

class OptionChainScreen extends StatefulWidget {
  const OptionChainScreen({super.key});

  @override
  State<OptionChainScreen> createState() => _OptionChainScreenState();
}

class _OptionChainScreenState extends State<OptionChainScreen> with TickerProviderStateMixin {
  String? selectedUnderlyingId;
  String searchQuery = 'NIFTY 50';
  late TabController _tabController;
  late TabController _greeksTabController;
  List<DateTime> expiryDates = [];
  DateTime? selectedExpiry;
  bool _sortAscending = true;
  String _selectedGreek = 'OI'; // OI, IV, Delta, Gamma, Theta, Vega
  bool _showAbsoluteChange = true; // true for absolute, false for percentage

  final List<String> _greekOptions = ['OI', 'IV', 'Delta', 'Gamma', 'Theta', 'Vega'];

  // Store reference to the bloc to avoid context access in dispose
  OptionGreeksWebSocketBloc? _optionGreeksBloc;

  // Available underlying assets for option chain
  final List<Map<String, dynamic>> _availableUnderlyings = [
    {
      'name': 'NIFTY 50',
      'symbol': 'NIFTY',
      'price': 24866.65,
      'change': -195.45,
      'changePercent': -0.77,
    },
    {
      'name': 'BANK NIFTY',
      'symbol': 'BANKNIFTY',
      'price': 51234.80,
      'change': 234.50,
      'changePercent': 0.46,
    },
  ];

  // Mock data for demonstration - replace with actual data
  final Map<String, dynamic> mockData = {
    'NIFTY 50': {
      'price': 24866.65,
      'change': -195.45,
      'changePercent': -0.77,
    },
    'BANK NIFTY': {
      'price': 51234.80,
      'change': 234.50,
      'changePercent': 0.46,
    }
  };

  @override
  void initState() {
    super.initState();

    // Initialize with default underlying
    selectedUnderlyingId = 'NIFTY';

    // Initialize with empty tab controller
    _tabController = TabController(length: 0, vsync: this);
    _greeksTabController = TabController(length: _greekOptions.length, vsync: this);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Store bloc reference safely
    _optionGreeksBloc = context.read<OptionGreeksWebSocketBloc>();

    // Connect to option Greeks WebSocket when screen is opened
    final authState = context.read<AuthBloc>().state;
    if (authState is AuthAuthenticated) {
      _optionGreeksBloc?.add(
        OptionGreeksWebSocketConnect(authState.credentialsModel.accessToken)
      );
    }
  }

  void _initializeExpiryDates(List<ZenOptionGreeks> optionGreeksList) {
    final Set<DateTime> uniqueExpiries = {};
    
    for (final option in optionGreeksList) {
      if (option.hasExpiry()) {
        final expiry = DateTime(
          option.expiry.year,
          option.expiry.month,
          option.expiry.day,
        );
        uniqueExpiries.add(expiry);
      }
    }
    
    final sortedExpiries = uniqueExpiries.toList()..sort();
    
    if (expiryDates.length != sortedExpiries.length || 
        !expiryDates.every((date) => sortedExpiries.contains(date))) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            expiryDates = sortedExpiries;
            if (expiryDates.isNotEmpty && selectedExpiry == null) {
              selectedExpiry = expiryDates.first;
            }
            if (_tabController.length != expiryDates.length) {
              _tabController.dispose(); // Dispose old controller
              _tabController = TabController(length: expiryDates.length, vsync: this);
            }
          });
        }
      });
    }
  }

  List<ZenOptionGreeks> _filterByExpiry(List<ZenOptionGreeks> optionGreeksList) {
    if (selectedExpiry == null) return optionGreeksList;

    return optionGreeksList.where((option) {
      if (!option.hasExpiry()) return false;

      final optionExpiry = DateTime(
        option.expiry.year,
        option.expiry.month,
        option.expiry.day,
      );

      return optionExpiry.isAtSameMomentAs(selectedExpiry!);
    }).toList();
  }

  List<ZenOptionGreeks> _filterByUnderlying(List<ZenOptionGreeks> optionGreeksList) {
    if (selectedUnderlyingId == null) return optionGreeksList;

    return optionGreeksList.where((option) {
      if (!option.hasTradingSymbol()) return false;

      final symbol = option.tradingSymbol.value.toUpperCase();

      // Filter based on selected underlying
      switch (selectedUnderlyingId) {
        case 'NIFTY':
          return symbol.contains('NIFTY') && !symbol.contains('BANKNIFTY') && !symbol.contains('MIDCAP');
        case 'BANKNIFTY':
          return symbol.contains('BANKNIFTY');
        default:
          return true;
      }
    }).toList();
  }

  String _formatExpiryDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;

    final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                   'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

    final monthStr = months[date.month - 1];
    final dayStr = date.day.toString().padLeft(2, '0');

    // Format as a single line with clear, concise format
    if (difference == 0) {
      return '$dayStr $monthStr (Today)';
    } else if (difference == 1) {
      return '$dayStr $monthStr (Tomorrow)';
    } else if (difference <= 7) {
      return '$dayStr $monthStr (${difference}d)';
    } else if (difference <= 30) {
      final weeks = (difference / 7).round();
      return '$dayStr $monthStr (${weeks}w)';
    } else {
      final months = (difference / 30).round();
      return '$dayStr $monthStr (${months}m)';
    }
  }

  String _getGreekLabel() {
    switch (_selectedGreek) {
      case 'OI':
        return 'OI (in lakhs)';
      case 'IV':
        return 'IV (%)';
      case 'Delta':
        return 'Delta';
      case 'Gamma':
        return 'Gamma';
      case 'Theta':
        return 'Theta';
      case 'Vega':
        return 'Vega';
      default:
        return 'OI (in lakhs)';
    }
  }

  String _getGreekValue(ZenOptionGreeks? option) {
    if (option == null) return '0.00';
    
    switch (_selectedGreek) {
      case 'OI':
        // Mock OI calculation based on LTP
        final ltp = option.hasLastPrice() ? option.lastPrice.value : 0.0;
        return (ltp * 0.001).toStringAsFixed(2);
      case 'IV':
        return option.hasImpliedVolatility() 
            ? (option.impliedVolatility.value * 100).toStringAsFixed(2)
            : '0.00';
      case 'Delta':
        return option.hasDelta() 
            ? option.delta.value.toStringAsFixed(4)
            : '0.0000';
      case 'Gamma':
        return option.hasGamma() 
            ? option.gamma.value.toStringAsFixed(4)
            : '0.0000';
      case 'Theta':
        return option.hasTheta() 
            ? option.theta.value.toStringAsFixed(4)
            : '0.0000';
      case 'Vega':
        return option.hasVega() 
            ? option.vega.value.toStringAsFixed(4)
            : '0.0000';
      default:
        return '0.00';
    }
  }

  double _getGreekPercentage(ZenOptionGreeks? option, double strike) {
    if (option == null) return 0.0;
    
    switch (_selectedGreek) {
      case 'OI':
        final ltp = option.hasLastPrice() ? option.lastPrice.value : 0.0;
        return ltp > 0 ? ((ltp - strike * 0.1) / (strike * 0.1) * 100) : 0.0;
      case 'IV':
        final iv = option.hasImpliedVolatility() ? option.impliedVolatility.value : 0.0;
        return iv > 0 ? ((iv - 0.15) / 0.15 * 100) : 0.0;
      case 'Delta':
        final delta = option.hasDelta() ? option.delta.value : 0.0;
        return delta * 100; // Delta as percentage
      case 'Gamma':
        final gamma = option.hasGamma() ? option.gamma.value : 0.0;
        return gamma * 1000; // Gamma scaled for percentage display
      case 'Theta':
        final theta = option.hasTheta() ? option.theta.value : 0.0;
        return theta * 100; // Theta as percentage
      case 'Vega':
        final vega = option.hasVega() ? option.vega.value : 0.0;
        return vega * 10; // Vega scaled for percentage display
      default:
        return 0.0;
    }
  }

  ZenOptionGreeks? _getPrimaryOptionForSorting(ZenOptionGreeks? callOption, ZenOptionGreeks? putOption, double strike) {
    // For sorting, prefer the option with higher value or the call option if both are similar
    if (callOption == null && putOption == null) return null;
    if (callOption == null) return putOption;
    if (putOption == null) return callOption;

    final callValue = _getGreekValueForSorting(callOption, strike);
    final putValue = _getGreekValueForSorting(putOption, strike);

    return callValue >= putValue ? callOption : putOption;
  }

  double _getGreekValueForSorting(ZenOptionGreeks? option, double strike) {
    if (option == null) return 0.0;

    switch (_selectedGreek) {
      case 'OI':
        // Mock OI calculation based on LTP
        final ltp = option.hasLastPrice() ? option.lastPrice.value : 0.0;
        return ltp * 0.001;
      case 'IV':
        return option.hasImpliedVolatility()
            ? option.impliedVolatility.value * 100
            : 0.0;
      case 'Delta':
        return option.hasDelta()
            ? option.delta.value.abs()
            : 0.0;
      case 'Gamma':
        return option.hasGamma()
            ? option.gamma.value.abs()
            : 0.0;
      case 'Theta':
        return option.hasTheta()
            ? option.theta.value.abs()
            : 0.0;
      case 'Vega':
        return option.hasVega()
            ? option.vega.value.abs()
            : 0.0;
      default:
        return 0.0;
    }
  }

  Widget _buildFlowingIndicatorLine(double callPercentage, double putPercentage, ThemeState themeState) {
    // Calculate the width of each side based on percentage (max 80px each side for better proportion)
    const maxWidth = 80.0;
    final leftWidth = (callPercentage.abs() / 50 * maxWidth).clamp(0.0, maxWidth); // Scale based on 50% max
    final rightWidth = (putPercentage.abs() / 50 * maxWidth).clamp(0.0, maxWidth);

    // Determine colors based on positive/negative values with better contrast
    final leftColor = callPercentage >= 0 ? Colors.green.shade600 : Colors.red.shade600;
    final rightColor = putPercentage >= 0 ? Colors.green.shade600 : Colors.red.shade600;

    return SizedBox(
      height: 2,
      child: Row(
        children: [
          // Left side (Call) - grows from center to left
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  height: 2,
                  width: leftWidth,
                  decoration: BoxDecoration(
                    color: leftColor,
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ],
            ),
          ),
          // Center point (small gap)
          Container(
            width: 4,
            height: 2,
            color: AppTheme.borderColor(themeState.isDarkMode),
          ),
          // Right side (Put) - grows from center to right
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  height: 2,
                  width: rightWidth,
                  decoration: BoxDecoration(
                    color: rightColor,
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showUnderlyingSelector(ThemeState themeState) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Select Underlying',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            // Options list
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _availableUnderlyings.length,
              itemBuilder: (context, index) {
                final underlying = _availableUnderlyings[index];
                final isSelected = searchQuery == underlying['name'];

                return ListTile(
                  onTap: () {
                    setState(() {
                      searchQuery = underlying['name'];
                      selectedUnderlyingId = underlying['symbol'];
                      // Reset expiry selection when changing underlying
                      selectedExpiry = null;
                      expiryDates.clear();
                    });
                    Navigator.pop(context);
                  },
                  leading: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.2)
                          : AppTheme.cardColor(themeState.isDarkMode),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected
                            ? AppTheme.primaryColor(themeState.isDarkMode)
                            : AppTheme.borderColor(themeState.isDarkMode),
                      ),
                    ),
                    child: Center(
                      child: Text(
                        underlying['symbol'].substring(0, 2),
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: isSelected
                              ? AppTheme.primaryColor(themeState.isDarkMode)
                              : AppTheme.textSecondary(themeState.isDarkMode),
                        ),
                      ),
                    ),
                  ),
                  title: Text(
                    underlying['name'],
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                  subtitle: Text(
                    '${underlying['price']} (${underlying['change'] >= 0 ? '+' : ''}${underlying['change']} | ${underlying['changePercent']}%)',
                    style: TextStyle(
                      fontSize: 14,
                      color: underlying['change'] >= 0 ? Colors.green : Colors.red,
                    ),
                  ),
                  trailing: isSelected
                      ? Icon(
                          Icons.check_circle,
                          color: AppTheme.primaryColor(themeState.isDarkMode),
                        )
                      : null,
                );
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showOptionsMenu(ThemeState themeState) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Row(
                children: [
                  Text(
                    'Display Options',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            // Price Change Display Option
            ListTile(
              leading: Icon(
                _showAbsoluteChange ? Icons.currency_rupee : Icons.percent,
                color: AppTheme.primaryColor(themeState.isDarkMode),
              ),
              title: Text(
                'Price Change Display',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
              subtitle: Text(
                _showAbsoluteChange ? 'Absolute Change' : 'Percentage Change',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                ),
              ),
              trailing: Switch(
                value: _showAbsoluteChange,
                onChanged: (value) {
                  setState(() {
                    _showAbsoluteChange = value;
                  });
                  Navigator.pop(context);
                },
                activeColor: AppTheme.primaryColor(themeState.isDarkMode),
              ),
            ),
            // Sort by Greek Option
            ListTile(
              leading: Icon(
                Icons.sort,
                color: AppTheme.primaryColor(themeState.isDarkMode),
              ),
              title: Text(
                'Sort by $_selectedGreek',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
              subtitle: Text(
                _sortAscending ? 'Ascending' : 'Descending',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary(themeState.isDarkMode),
                ),
              ),
              trailing: Icon(
                _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                color: AppTheme.textSecondary(themeState.isDarkMode),
              ),
              onTap: () {
                setState(() {
                  _sortAscending = !_sortAscending;
                });
                Navigator.pop(context);
              },
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  void _showOptionDetails(ZenOptionGreeks option, ThemeState themeState) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle bar
            Container(
              margin: const EdgeInsets.only(top: 12, bottom: 20),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppTheme.textSecondary(themeState.isDarkMode),
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // Title with trading symbol
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              child: Row(
                children: [
                  Text(
                    option.hasTradingSymbol() ? option.tradingSymbol.value : 'Option Details',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: Icon(
                      Icons.close,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                  ),
                ],
              ),
            ),
            // Option details
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  _buildDetailRow('LTP', option.hasLastPrice() ? '₹${option.lastPrice.value.toStringAsFixed(2)}' : 'N/A', themeState),
                  _buildDetailRow('Strike', option.hasStrike() ? option.strike.value.toInt().toString() : 'N/A', themeState),
                  _buildDetailRow('IV', option.hasImpliedVolatility() ? '${(option.impliedVolatility.value * 100).toStringAsFixed(2)}%' : 'N/A', themeState),
                  _buildDetailRow('Delta', option.hasDelta() ? option.delta.value.toStringAsFixed(4) : 'N/A', themeState),
                  _buildDetailRow('Gamma', option.hasGamma() ? option.gamma.value.toStringAsFixed(4) : 'N/A', themeState),
                  _buildDetailRow('Theta', option.hasTheta() ? option.theta.value.toStringAsFixed(4) : 'N/A', themeState),
                  _buildDetailRow('Vega', option.hasVega() ? option.vega.value.toStringAsFixed(4) : 'N/A', themeState),
                ],
              ),
            ),
            const SizedBox(height: 20),
            // Action buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                children: [
                  // Buy and Sell buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: () => _openOrderForm(option, 'BUY'),
                          child: const Text(
                            'BUY',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            padding: const EdgeInsets.symmetric(vertical: 14),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: () => _openOrderForm(option, 'SELL'),
                          child: const Text(
                            'SELL',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Additional action buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: BorderSide(color: AppTheme.primaryColor(themeState.isDarkMode)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: () {
                            // View chart functionality can be added later
                            Navigator.pop(context);
                          },
                          icon: Icon(
                            Icons.bar_chart,
                            color: AppTheme.primaryColor(themeState.isDarkMode),
                          ),
                          label: Text(
                            'View chart',
                            style: TextStyle(
                              color: AppTheme.primaryColor(themeState.isDarkMode),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: OutlinedButton.icon(
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            side: BorderSide(color: AppTheme.primaryColor(themeState.isDarkMode)),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          onPressed: () => _addToWatchlist(option),
                          icon: Icon(
                            Icons.add,
                            color: AppTheme.primaryColor(themeState.isDarkMode),
                          ),
                          label: Text(
                            'Add to watchlist',
                            style: TextStyle(
                              color: AppTheme.primaryColor(themeState.isDarkMode),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, ThemeState themeState) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              color: AppTheme.textSecondary(themeState.isDarkMode),
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
        ],
      ),
    );
  }

  void _openOrderForm(ZenOptionGreeks option, String action) {
    Navigator.pop(context); // Close the details modal first

    // Find the security from the cached security list
    final secListState = context.read<SecurityListBloc>().state;
    SecurityModel? selectedSecurity;

    if (secListState is SecurityListLoaded && option.hasZenId()) {
      // Search in both equity and features lists
      final zenIdValue = option.zenId.value.toInt();
      selectedSecurity = secListState.equityList.firstWhere(
        (data) => data.zenId == zenIdValue,
        orElse: () => secListState.featuresList.firstWhere(
          (data) => data.zenId == zenIdValue,
          orElse: () => SecurityModel(
            zenId: option.hasZenId() ? option.zenId.value.toInt() : 0,
            tradingSymbol: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
            strike: option.hasStrike() ? option.strike.value : 0.0,
            exchanges: ["NSE"],
            lotSize: 1,
            instrumentType: "OPT",
            expiryType: "MONTHLY",
            expiry: option.hasExpiry() ? "${option.expiry.year}-${option.expiry.month.toString().padLeft(2, '0')}-${option.expiry.day.toString().padLeft(2, '0')}" : null,
            name: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
          ),
        ),
      );
    }

    if (selectedSecurity != null) {
      showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        isScrollControlled: true,
        builder: (modalContext) {
          return SingleChildScrollView(
            reverse: true,
            child: OrderFormSheet(
              openOrderType: FormOpenOrderType.create,
              // You may need to create a custom constructor or modify OrderFormSheet
              // to accept a SecurityModel directly for pre-selection
            ),
          );
        },
      );
    } else {
      ToastUtil.showToast(
        context,
        'Security not found in cache',
        ToastType.error,
      );
    }
  }

  void _addToWatchlist(ZenOptionGreeks option) async {
    Navigator.pop(context); // Close the details modal first

    final authState = context.read<AuthBloc>().state;
    if (authState is! AuthAuthenticated) {
      ToastUtil.showToast(
        context,
        'Please login to add to watchlist',
        ToastType.error,
      );
      return;
    }

    // Find the security from the cached security list
    final secListState = context.read<SecurityListBloc>().state;
    SecurityModel? selectedSecurity;

    if (secListState is SecurityListLoaded && option.hasZenId()) {
      // Search in both equity and features lists
      final zenIdValue = option.zenId.value.toInt();
      selectedSecurity = secListState.equityList.firstWhere(
        (data) => data.zenId == zenIdValue,
        orElse: () => secListState.featuresList.firstWhere(
          (data) => data.zenId == zenIdValue,
          orElse: () => SecurityModel(
            zenId: option.hasZenId() ? option.zenId.value.toInt() : 0,
            tradingSymbol: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
            strike: option.hasStrike() ? option.strike.value : 0.0,
            exchanges: ["NSE"],
            lotSize: 1,
            instrumentType: "OPT",
            expiryType: "MONTHLY",
            expiry: option.hasExpiry() ? "${option.expiry.year}-${option.expiry.month.toString().padLeft(2, '0')}-${option.expiry.day.toString().padLeft(2, '0')}" : null,
            name: option.hasTradingSymbol() ? option.tradingSymbol.value : "Unknown",
          ),
        ),
      );
    }

    if (selectedSecurity != null) {
      try {
        final watchlistService = WatchlistService();
        await watchlistService.addSecurityToWatchlist(
          authState.credentialsModel.clientId.toString(),
          0, // Always add to first watchlist (index 0)
          selectedSecurity,
        );

        if (mounted) {
          ToastUtil.showToast(
            context,
            '${selectedSecurity.tradingSymbol} added to watchlist',
            ToastType.success,
          );
        }
      } catch (e) {
        if (mounted) {
          ToastUtil.showToast(
            context,
            'Error adding to watchlist: $e',
            ToastType.error,
          );
        }
      }
    } else {
      if (mounted) {
        ToastUtil.showToast(
          context,
          'Security not found in cache',
          ToastType.error,
        );
      }
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _greeksTabController.dispose();
    // Disconnect from option Greeks WebSocket when screen is closed
    // Use stored bloc reference to avoid context access after dispose
    _optionGreeksBloc?.add(OptionGreeksWebSocketDisconnect());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeBloc, ThemeState>(
      builder: (context, themeState) {
        return Scaffold(
          backgroundColor: AppTheme.backgroundColor(themeState.isDarkMode),
          appBar: _buildAppBar(themeState),
          body: BlocBuilder<OptionGreeksWebSocketBloc, OptionGreeksWebSocketState>(
            builder: (context, state) {
              if (state is OptionGreeksWebSocketConnecting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state is OptionGreeksWebSocketError) {
                return _buildErrorState(state.error ?? 'Unknown error occurred', themeState);
              }

              if (state is OptionGreeksWebSocketDisconnectedState) {
                return _buildDisconnectedState(themeState);
              }

              final optionGreeksList = state.optionGreeksList ?? [];

              if (optionGreeksList.isEmpty) {
                return _buildEmptyState(themeState);
              }

              // Filter options by selected underlying first
              final underlyingFilteredOptions = _filterByUnderlying(optionGreeksList);

              // Initialize expiry dates from filtered data
              _initializeExpiryDates(underlyingFilteredOptions);

              // Filter options by selected expiry
              final filteredOptions = _filterByExpiry(underlyingFilteredOptions);

              return Column(
                children: [
                  _buildSearchBar(themeState),
                  if (expiryDates.isNotEmpty) _buildExpiryTabs(themeState),
                  _buildGreeksTabs(themeState),
                  _buildOptionChainTable(filteredOptions, themeState),
                  _buildBottomStats(filteredOptions, themeState),
                ],
              );
            },
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(ThemeState themeState) {
    return AppBar(
      backgroundColor: AppTheme.surfaceColor(themeState.isDarkMode),
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back, color: AppTheme.textPrimary(themeState.isDarkMode)),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          Text(
            'Option Chain',
            style: TextStyle(
              color: AppTheme.textSecondary(themeState.isDarkMode),
              fontSize: 18,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.bar_chart, color: AppTheme.textPrimary(themeState.isDarkMode)),
          onPressed: () {},
        ),
        IconButton(
          icon: Icon(Icons.more_vert, color: AppTheme.textPrimary(themeState.isDarkMode)),
          onPressed: () => _showOptionsMenu(themeState),
        ),
      ],
    );
  }

  Widget _buildSearchBar(ThemeState themeState) {
    final data = mockData[searchQuery];
    return GestureDetector(
      onTap: () => _showUnderlyingSelector(themeState),
      child: Container(
        margin: const EdgeInsets.all(16),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: AppTheme.surfaceColor(themeState.isDarkMode),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.borderColor(themeState.isDarkMode)),
        ),
        child: Row(
          children: [
            Icon(Icons.search, color: AppTheme.textSecondary(themeState.isDarkMode), size: 20),
            const SizedBox(width: 12),
            Text(
              searchQuery,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary(themeState.isDarkMode),
              ),
            ),
            const SizedBox(width: 8),
            Icon(Icons.keyboard_arrow_down, color: AppTheme.textSecondary(themeState.isDarkMode), size: 20),
            const Spacer(),
            if (data != null) ...[
              Text(
                '${data['price']}',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary(themeState.isDarkMode),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                _showAbsoluteChange
                    ? '${data['change'] >= 0 ? '+' : ''}${data['change']}'
                    : '${data['changePercent'] >= 0 ? '+' : ''}${data['changePercent']}%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: data['change'] >= 0 ? Colors.green : Colors.red,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildExpiryTabs(ThemeState themeState) {
    if (expiryDates.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 32,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary(themeState.isDarkMode),
        indicator: BoxDecoration(
          color: AppTheme.primaryColor(themeState.isDarkMode),
          borderRadius: BorderRadius.circular(16),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorPadding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
        dividerColor: Colors.transparent,
        labelPadding: const EdgeInsets.symmetric(horizontal: 8),
        labelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          letterSpacing: 0.5,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          letterSpacing: 0.3,
        ),
        onTap: (index) {
          setState(() {
            selectedExpiry = expiryDates[index];
          });
        },
        tabs: expiryDates.map((date) {
          return Tab(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppTheme.borderColor(themeState.isDarkMode).withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: Text(
                _formatExpiryDate(date),
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  letterSpacing: 0.4,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.visible,
                softWrap: false,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGreeksTabs(ThemeState themeState) {
    // Light blue color for modern UI
    const lightBlueColor = Color(0xFF64B5F6); // Material Design Light Blue 300

    return Container(
      height: 35,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: TabBar(
        controller: _greeksTabController,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.textSecondary(themeState.isDarkMode),
        indicator: BoxDecoration(
          color: lightBlueColor,
          borderRadius: BorderRadius.circular(15),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelStyle: const TextStyle(fontSize: 11, fontWeight: FontWeight.bold),
        unselectedLabelStyle: const TextStyle(fontSize: 11, fontWeight: FontWeight.normal),
        isScrollable: true,
        onTap: (index) {
          setState(() {
            _selectedGreek = _greekOptions[index];
          });
        },
        tabs: _greekOptions.map((greek) {
          return Tab(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Text(
                greek,
                style: const TextStyle(fontSize: 11),
                textAlign: TextAlign.center,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildOptionChainTable(List<ZenOptionGreeks> optionGreeksList, ThemeState themeState) {
    // Group options by strike price and separate calls/puts
    final Map<double, Map<String, ZenOptionGreeks>> groupedOptions = {};
    
    for (final option in optionGreeksList) {
      if (!option.hasStrike()) continue;
      
      final strike = option.strike.value;
      if (!groupedOptions.containsKey(strike)) {
        groupedOptions[strike] = {};
      }
      
      // Determine if it's a call or put based on trading symbol
      final symbol = option.hasTradingSymbol() ? option.tradingSymbol.value : '';
      final isCall = symbol.contains('CE') || symbol.contains('CALL');
      final isPut = symbol.contains('PE') || symbol.contains('PUT');
      
      if (isCall) {
        groupedOptions[strike]!['call'] = option;
      } else if (isPut) {
        groupedOptions[strike]!['put'] = option;
      }
    }
    
    // Sort strikes based on current sort order and selected Greek
    final sortedStrikes = groupedOptions.keys.toList();

    // Sort by selected Greek value if not sorting by strike
    sortedStrikes.sort((a, b) {
      // Get the options for both strikes
      final optionsA = groupedOptions[a]!;
      final optionsB = groupedOptions[b]!;

      // Get the primary option (call or put with higher value) for comparison
      final optionA = _getPrimaryOptionForSorting(optionsA['call'], optionsA['put'], a);
      final optionB = _getPrimaryOptionForSorting(optionsB['call'], optionsB['put'], b);

      // Get Greek values for comparison
      final valueA = _getGreekValueForSorting(optionA, a);
      final valueB = _getGreekValueForSorting(optionB, b);

      int comparison = valueA.compareTo(valueB);
      return _sortAscending ? comparison : -comparison;
    });
    
    return Expanded(
      child: Column(
        children: [
          // Table Header
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: AppTheme.surfaceColor(themeState.isDarkMode),
              border: Border(
                bottom: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    _getGreekLabel(),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Call LTP',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        _sortAscending = !_sortAscending;
                      });
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          'Strike',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textSecondary(themeState.isDarkMode),
                          ),
                        ),
                        const SizedBox(width: 4),
                        Icon(
                          _sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                          size: 12,
                          color: AppTheme.textSecondary(themeState.isDarkMode),
                        ),
                      ],
                    ),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    'Put LTP',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    _getGreekLabel(),
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textSecondary(themeState.isDarkMode),
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          // Table Body
          Expanded(
            child: ListView.builder(
              itemCount: sortedStrikes.length,
              itemBuilder: (context, index) {
                final strike = sortedStrikes[index];
                final options = groupedOptions[strike]!;
                final callOption = options['call'];
                final putOption = options['put'];
                
                return _buildOptionRow(strike, callOption, putOption, themeState);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptionRow(double strike, ZenOptionGreeks? callOption, ZenOptionGreeks? putOption, ThemeState themeState) {
    // Get LTP values
    final callLTP = (callOption?.hasLastPrice() == true) ? callOption!.lastPrice.value : 0.0;
    final putLTP = (putOption?.hasLastPrice() == true) ? putOption!.lastPrice.value : 0.0;

    // Get Greek values based on selected Greek
    final callGreekValue = _getGreekValue(callOption);
    final putGreekValue = _getGreekValue(putOption);

    // Get percentage changes
    final callGreekPercentage = _getGreekPercentage(callOption, strike);
    final putGreekPercentage = _getGreekPercentage(putOption, strike);
    final callLTPPercentage = callLTP > 0 ? ((callLTP - strike * 0.1) / (strike * 0.1) * 100) : 0.0;
    final putLTPPercentage = putLTP > 0 ? ((putLTP - strike * 0.05) / (strike * 0.05) * 100) : 0.0;

    // No longer need individual indicator colors since line is under strike

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: AppTheme.borderColor(themeState.isDarkMode).withValues(alpha: 0.3),
          ),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
          // Call Greek Value
          Expanded(
            flex: 2,
            child: _buildOptionCellWithoutIndicator(
              value: callGreekValue,
              percentage: '${callGreekPercentage.toStringAsFixed(1)}%',
              isPositive: callGreekPercentage >= 0,
              themeState: themeState,
              option: callOption,
            ),
          ),
          // Call LTP
          Expanded(
            flex: 2,
            child: _buildOptionCellWithoutIndicator(
              value: callLTP.toStringAsFixed(2),
              percentage: '${callLTPPercentage.toStringAsFixed(1)}%',
              isPositive: callLTPPercentage >= 0,
              themeState: themeState,
              option: callOption,
            ),
          ),
          // Strike
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              decoration: BoxDecoration(
                color: AppTheme.primaryColor(themeState.isDarkMode).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: AppTheme.primaryColor(themeState.isDarkMode),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    strike.toInt().toString(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary(themeState.isDarkMode),
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
          // Put LTP
          Expanded(
            flex: 2,
            child: _buildOptionCellWithoutIndicator(
              value: putLTP.toStringAsFixed(2),
              percentage: '${putLTPPercentage.toStringAsFixed(1)}%',
              isPositive: putLTPPercentage >= 0,
              themeState: themeState,
              option: putOption,
            ),
          ),
          // Put Greek Value
          Expanded(
            flex: 2,
            child: _buildOptionCellWithoutIndicator(
              value: putGreekValue,
              percentage: '${putGreekPercentage.toStringAsFixed(1)}%',
              isPositive: putGreekPercentage >= 0,
              themeState: themeState,
              option: putOption,
            ),
          ),
            ],
          ),
          const SizedBox(height: 8),
          // Flowing indicator line across the entire row
          _buildFlowingIndicatorLine(callGreekPercentage, putGreekPercentage, themeState),
        ],
      ),
    );
  }

  Widget _buildOptionCellWithoutIndicator({
    required String value,
    required String percentage,
    required bool isPositive,
    required ThemeState themeState,
    ZenOptionGreeks? option,
  }) {
    Widget cellContent = Column(
      children: [
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary(themeState.isDarkMode),
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 2),
        Text(
          percentage,
          style: TextStyle(
            fontSize: 12,
            color: isPositive ? Colors.green : Colors.red,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );

    // Make the cell clickable if option is provided
    if (option != null) {
      return GestureDetector(
        onTap: () => _showOptionDetails(option, themeState),
        child: cellContent,
      );
    }

    return cellContent;
  }

  Widget _buildBottomStats(List<ZenOptionGreeks> filteredOptions, ThemeState themeState) {
    // Calculate stats from actual data
    final callOptions = filteredOptions.where((option) {
      final symbol = option.hasTradingSymbol() ? option.tradingSymbol.value : '';
      return symbol.contains('CE') || symbol.contains('CALL');
    }).toList();
    
    final putOptions = filteredOptions.where((option) {
      final symbol = option.hasTradingSymbol() ? option.tradingSymbol.value : '';
      return symbol.contains('PE') || symbol.contains('PUT');
    }).toList();
    
    // Calculate PCR (Put-Call Ratio) - simplified calculation
    final pcr = putOptions.isNotEmpty && callOptions.isNotEmpty 
        ? (putOptions.length / callOptions.length).toStringAsFixed(2)
        : '0.00';
    
    // Calculate average IV
    final allIVs = filteredOptions
        .where((option) => option.hasImpliedVolatility())
        .map((option) => option.impliedVolatility.value)
        .toList();
    
    final avgIV = allIVs.isNotEmpty 
        ? (allIVs.reduce((a, b) => a + b) / allIVs.length).toStringAsFixed(2)
        : '0.00';
    
    // Find strike with most activity (simplified max pain calculation)
    final strikeActivity = <double, int>{};
    for (final option in filteredOptions) {
      if (option.hasStrike()) {
        strikeActivity[option.strike.value] = (strikeActivity[option.strike.value] ?? 0) + 1;
      }
    }
    
    final maxPain = strikeActivity.isNotEmpty 
        ? strikeActivity.entries.reduce((a, b) => a.value > b.value ? a : b).key.toInt().toString()
        : '0';

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceColor(themeState.isDarkMode),
        border: Border(
          top: BorderSide(color: AppTheme.borderColor(themeState.isDarkMode)),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('PCR', pcr, themeState),
          _buildStatItem('Max Pain', maxPain, themeState),
          _buildStatItem('ATM IV', avgIV, themeState),
          _buildStatItem('IV Percentile', '$avgIV - ${(double.tryParse(avgIV) ?? 0) > 15 ? 'High' : 'Low'}', themeState),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, ThemeState themeState) {
    return Column(
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: AppTheme.textSecondary(themeState.isDarkMode),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary(themeState.isDarkMode),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState(String error, ThemeState themeState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red),
          const SizedBox(height: 16),
          Text(
            'Connection Error',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDisconnectedState(ThemeState themeState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.wifi_off, size: 64, color: Colors.orange),
          const SizedBox(height: 16),
          Text(
            'Connection Lost',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Attempting to reconnect...',
            style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode)),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(ThemeState themeState) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.data_usage_outlined,
            size: 64,
            color: AppTheme.textSecondary(themeState.isDarkMode),
          ),
          const SizedBox(height: 16),
          Text(
            'No Option Data',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary(themeState.isDarkMode),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Waiting for option Greeks data...',
            style: TextStyle(color: AppTheme.textSecondary(themeState.isDarkMode)),
          ),
        ],
      ),
    );
  }
}